import { useState, useEffect, useCallback } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { supabase, RoomVideoState, createAuthenticatedSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';
import { useSmartRefresh } from './useSmartRefresh';

export interface VideoStateUpdate {
  video_url?: string;
  video_type?: 'youtube' | 'file' | 'url';
  youtube_video_id?: string;
  is_playing?: boolean;
  video_current_time?: number;
  video_duration?: number;
  playback_rate?: number;
}

export interface PreciseSyncData {
  room_id: string;
  is_playing: boolean;
  video_current_time: number;
  video_duration: number;
  playback_rate: number;
  sync_version: number;
  server_time: string;
  last_sync_timestamp: string;
  client_server_diff: number;
  video_url?: string;
  video_type: 'youtube' | 'file' | 'url';
  youtube_video_id?: string;
}

export const useRoomVideoState = (roomId: string, isOwner: boolean) => {
  const { user } = useUser();
  const { getToken, isSignedIn } = useAuth();
  const [videoState, setVideoState] = useState<RoomVideoState | null>(null);
  const [loading, setLoading] = useState(true);
  const [preciseSyncData, setPreciseSyncData] = useState<PreciseSyncData | null>(null);
  const [lastSyncVersion, setLastSyncVersion] = useState<number>(0);
  const [networkLatency, setNetworkLatency] = useState<number>(0);

  // Get authenticated Supabase client
  const getAuthenticatedClient = useCallback(async () => {
    if (!isSignedIn) {
      return supabase;
    }

    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        return createAuthenticatedSupabaseClient(token);
      }
      return supabase;
    } catch (error) {
      console.error('Error getting authenticated client:', error);
      return supabase;
    }
  }, [getToken, isSignedIn]);

  // Fetch current video state
  const fetchVideoState = useCallback(async () => {
    if (!roomId) return;

    try {
      const client = await getAuthenticatedClient();
      const { data, error } = await client
        .from('room_video_state')
        .select('*')
        .eq('room_id', roomId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      setVideoState(data || null);
    } catch (error) {
      console.error('Error fetching video state:', error);
      toast.error('Failed to load video state');
    } finally {
      setLoading(false);
    }
  }, [roomId, getAuthenticatedClient]);

  // Get precise sync data with network compensation
  const getPreciseSyncData = useCallback(async () => {
    if (!roomId) return null;

    try {
      const client = await getAuthenticatedClient();
      const clientTimestamp = new Date().toISOString();

      const { data, error } = await client.rpc('get_precise_sync_time', {
        p_room_id: roomId,
        p_client_timestamp: clientTimestamp
      });

      if (error) throw error;

      if (data && !data.error) {
        setPreciseSyncData(data);
        setNetworkLatency(Math.abs(data.client_server_diff || 0));
        return data;
      }

      return null;
    } catch (error) {
      console.error('Error getting precise sync data:', error);
      return null;
    }
  }, [roomId, getAuthenticatedClient]);

  // Enhanced update function using the database function
  const updateVideoState = useCallback(async (updates: VideoStateUpdate) => {
    if (!user || !roomId || !isOwner) return;

    try {
      const client = await getAuthenticatedClient();

      const { data, error } = await client.rpc('update_video_sync_state', {
        p_room_id: roomId,
        p_user_id: user.id,
        p_is_playing: updates.is_playing,
        p_current_time: updates.video_current_time,
        p_playback_rate: updates.playback_rate,
        p_video_url: updates.video_url,
        p_video_type: updates.video_type,
        p_youtube_video_id: updates.youtube_video_id,
        p_duration: updates.video_duration
      });

      if (error) throw error;

      // Immediately get updated precise sync data
      await getPreciseSyncData();

    } catch (error) {
      console.error('Error updating video state:', error);
      toast.error('Failed to update video state');
    }
  }, [user, roomId, isOwner, getAuthenticatedClient, getPreciseSyncData]);

  // Convenience methods for common operations with precision timing
  const setVideoUrl = useCallback((url: string, type: 'youtube' | 'file' | 'url', youtubeId?: string) => {
    updateVideoState({
      video_url: url,
      video_type: type,
      youtube_video_id: youtubeId,
      is_playing: false,
      video_current_time: 0,
      playback_rate: 1.0
    });
  }, [updateVideoState]);

  const setPlaying = useCallback((playing: boolean, currentTime?: number) => {
    const updates: VideoStateUpdate = { is_playing: playing };
    if (currentTime !== undefined) {
      updates.video_current_time = currentTime;
    }
    updateVideoState(updates);
  }, [updateVideoState]);

  const setCurrentTime = useCallback((time: number) => {
    updateVideoState({ video_current_time: time });
  }, [updateVideoState]);

  const setDuration = useCallback((duration: number) => {
    updateVideoState({ video_duration: duration });
  }, [updateVideoState]);

  const setPlaybackRate = useCallback((rate: number) => {
    updateVideoState({ playback_rate: rate });
  }, [updateVideoState]);

  // Clear video from room
  const clearVideo = useCallback(async () => {
    if (!user || !roomId || !isOwner) return;

    try {
      const client = await getAuthenticatedClient();

      // If there's a current video and it's a file upload, try to delete it from storage
      if (videoState?.video_type === 'file' && videoState?.video_url) {
        try {
          // Extract the file path from the public URL
          const url = new URL(videoState.video_url);
          const pathParts = url.pathname.split('/');
          const fileName = pathParts[pathParts.length - 1];
          const roomFolder = pathParts[pathParts.length - 2];
          const filePath = `${roomFolder}/${fileName}`;

          // Delete the file from storage
          const { error: deleteError } = await client.storage
            .from('videos')
            .remove([filePath]);

          if (deleteError) {
            console.warn('Failed to delete video file from storage:', deleteError);
            // Don't throw here - we still want to clear the video state
          }
        } catch (storageError) {
          console.warn('Error deleting video file:', storageError);
          // Continue with clearing the video state
        }
      }

      // Clear the video state
      const { error } = await client
        .from('room_video_state')
        .delete()
        .eq('room_id', roomId);

      if (error) throw error;

      // State will be updated via real-time subscription
      toast.success('Video removed successfully!');
    } catch (error) {
      console.error('Error clearing video:', error);
      toast.error('Failed to remove video');
    }
  }, [user, roomId, isOwner, getAuthenticatedClient, videoState]);

  // High-frequency precision sync for non-owners (cinema experience)
  useEffect(() => {
    if (!roomId || isOwner) return;

    const syncInterval = setInterval(async () => {
      const syncData = await getPreciseSyncData();
      if (syncData && syncData.sync_version > lastSyncVersion) {
        setLastSyncVersion(syncData.sync_version);
      }
    }, 500); // 500ms for smooth sync

    return () => clearInterval(syncInterval);
  }, [roomId, isOwner, getPreciseSyncData, lastSyncVersion]);

  // Smart background refresh for video state using utility hook
  useSmartRefresh({
    refreshFunction: fetchVideoState,
    enabled: !!roomId,
    connectionStatus: 'disconnected', // Default to disconnected for fallback polling
    idleThreshold: 5000, // 5 seconds for video state (more responsive)
    connectedInterval: 30000, // 30 seconds when real-time works
    disconnectedInterval: 10000, // 10 seconds when real-time fails (much more frequent)
    debug: false
  });

  // Set up real-time subscription
  useEffect(() => {
    if (!roomId) return;

    let channel: any = null;

    const setupSubscription = async () => {
      try {
        console.log('Setting up video state real-time subscription for room:', roomId);

        // Initial fetch
        await fetchVideoState();

        // Set up real-time subscription with authenticated client
        const client = await getAuthenticatedClient();
        channel = client
          .channel(`room_video_state:${roomId}`)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'room_video_state',
              filter: `room_id=eq.${roomId}`
            },
            (payload) => {
              console.log('Video state change detected:', payload);
              if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
                const newState = payload.new as RoomVideoState;
                setVideoState(newState);
              } else if (payload.eventType === 'DELETE') {
                setVideoState(null);
              }
            }
          )
          .subscribe((status, err) => {
            console.log('Video state subscription status:', status);
            if (err) {
              console.error('Video state subscription error:', err);
              toast.error('Failed to connect to video sync');
              return;
            }

            if (status === 'SUBSCRIBED') {
              console.log('Successfully subscribed to video state changes');
            } else if (status === 'CHANNEL_ERROR') {
              console.error('Video state channel error occurred');
              toast.error('Video sync connection error');
            } else if (status === 'TIMED_OUT') {
              console.error('Video state subscription timed out');
              toast.error('Video sync connection timed out');
            } else if (status === 'CLOSED') {
              console.log('Video state subscription closed');
            }
          });
      } catch (error) {
        console.error('Error setting up video state subscription:', error);
        toast.error('Failed to connect to video sync');
      }
    };

    setupSubscription();

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [roomId, fetchVideoState, getAuthenticatedClient]);

  return {
    videoState,
    loading,
    updateVideoState,
    setVideoUrl,
    setPlaying,
    setCurrentTime,
    setDuration,
    setPlaybackRate,
    clearVideo,
    preciseSyncData,
    getPreciseSyncData,
    networkLatency,
    lastSyncVersion
  };
};
